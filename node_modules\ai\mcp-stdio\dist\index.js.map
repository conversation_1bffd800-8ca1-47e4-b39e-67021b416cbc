{"version": 3, "sources": ["../index.ts", "../../core/tool/mcp/json-rpc-message.ts", "../../core/tool/mcp/types.ts", "../../errors/index.ts", "../../errors/mcp-client-error.ts", "../create-child-process.ts", "../get-environment.ts", "../mcp-stdio-transport.ts"], "sourcesContent": ["export {\n  StdioMCPTransport as Experimental_StdioMCPTransport,\n  type StdioConfig,\n} from './mcp-stdio-transport';\n", "import { z } from 'zod';\nimport { BaseParamsSchema, RequestSchema, ResultSchema } from './types';\n\nconst JSONRPC_VERSION = '2.0';\n\nconst JSONRPCRequestSchema = z\n  .object({\n    jsonrpc: z.literal(JSONRPC_VERSION),\n    id: z.union([z.string(), z.number().int()]),\n  })\n  .merge(RequestSchema)\n  .strict();\n\nexport type JSONRPCRequest = z.infer<typeof JSONRPCRequestSchema>;\n\nconst JSONRPCResponseSchema = z\n  .object({\n    jsonrpc: z.literal(JSONRPC_VERSION),\n    id: z.union([z.string(), z.number().int()]),\n    result: ResultSchema,\n  })\n  .strict();\n\nexport type JSONRPCResponse = z.infer<typeof JSONRPCResponseSchema>;\n\nconst JSONRPCErrorSchema = z\n  .object({\n    jsonrpc: z.literal(JSONRPC_VERSION),\n    id: z.union([z.string(), z.number().int()]),\n    error: z.object({\n      code: z.number().int(),\n      message: z.string(),\n      data: z.optional(z.unknown()),\n    }),\n  })\n  .strict();\n\nexport type JSONRPCError = z.infer<typeof JSONRPCErrorSchema>;\n\nconst JSONRPCNotificationSchema = z\n  .object({\n    jsonrpc: z.literal(JSONRPC_VERSION),\n  })\n  .merge(\n    z.object({\n      method: z.string(),\n      params: z.optional(BaseParamsSchema),\n    }),\n  )\n  .strict();\n\nexport type JSONRPCNotification = z.infer<typeof JSONRPCNotificationSchema>;\n\nexport const JSONRPCMessageSchema = z.union([\n  JSONRPCRequestSchema,\n  JSONRPCNotificationSchema,\n  JSONRPCResponseSchema,\n  JSONRPCErrorSchema,\n]);\n\nexport type JSONRPCMessage = z.infer<typeof JSONRPCMessageSchema>;\n", "import { z } from 'zod';\nimport {\n  inferParameters,\n  Tool,\n  ToolExecutionOptions,\n  ToolParameters,\n} from '../tool';\n\nexport const LATEST_PROTOCOL_VERSION = '2024-11-05';\nexport const SUPPORTED_PROTOCOL_VERSIONS = [\n  LATEST_PROTOCOL_VERSION,\n  '2024-10-07',\n];\n\nexport type ToolSchemas =\n  | Record<string, { parameters: ToolParameters }>\n  | 'automatic'\n  | undefined;\n\nexport type McpToolSet<TOOL_SCHEMAS extends ToolSchemas = 'automatic'> =\n  TOOL_SCHEMAS extends Record<string, { parameters: ToolParameters }>\n    ? {\n        [K in keyof TOOL_SCHEMAS]: Tool<\n          TOOL_SCHEMAS[K]['parameters'],\n          CallToolResult\n        > & {\n          execute: (\n            args: inferParameters<TOOL_SCHEMAS[K]['parameters']>,\n            options: ToolExecutionOptions,\n          ) => PromiseLike<CallToolResult>;\n        };\n      }\n    : {\n        [k: string]: Tool<z.ZodUnknown, CallToolResult> & {\n          execute: (\n            args: unknown,\n            options: ToolExecutionOptions,\n          ) => PromiseLike<CallToolResult>;\n        };\n      };\n\nconst ClientOrServerImplementationSchema = z\n  .object({\n    name: z.string(),\n    version: z.string(),\n  })\n  .passthrough();\nexport type Configuration = z.infer<typeof ClientOrServerImplementationSchema>;\n\nexport const BaseParamsSchema = z\n  .object({\n    _meta: z.optional(z.object({}).passthrough()),\n  })\n  .passthrough();\ntype BaseParams = z.infer<typeof BaseParamsSchema>;\nexport const ResultSchema = BaseParamsSchema;\n\nexport const RequestSchema = z.object({\n  method: z.string(),\n  params: z.optional(BaseParamsSchema),\n});\nexport type Request = z.infer<typeof RequestSchema>;\nexport type RequestOptions = {\n  signal?: AbortSignal;\n  timeout?: number;\n  maxTotalTimeout?: number;\n};\n\nexport type Notification = z.infer<typeof RequestSchema>;\n\nconst ServerCapabilitiesSchema = z\n  .object({\n    experimental: z.optional(z.object({}).passthrough()),\n    logging: z.optional(z.object({}).passthrough()),\n    prompts: z.optional(\n      z\n        .object({\n          listChanged: z.optional(z.boolean()),\n        })\n        .passthrough(),\n    ),\n    resources: z.optional(\n      z\n        .object({\n          subscribe: z.optional(z.boolean()),\n          listChanged: z.optional(z.boolean()),\n        })\n        .passthrough(),\n    ),\n    tools: z.optional(\n      z\n        .object({\n          listChanged: z.optional(z.boolean()),\n        })\n        .passthrough(),\n    ),\n  })\n  .passthrough();\nexport type ServerCapabilities = z.infer<typeof ServerCapabilitiesSchema>;\n\nexport const InitializeResultSchema = ResultSchema.extend({\n  protocolVersion: z.string(),\n  capabilities: ServerCapabilitiesSchema,\n  serverInfo: ClientOrServerImplementationSchema,\n  instructions: z.optional(z.string()),\n});\nexport type InitializeResult = z.infer<typeof InitializeResultSchema>;\n\nexport type PaginatedRequest = Request & {\n  params?: BaseParams & {\n    cursor?: string;\n  };\n};\n\nconst PaginatedResultSchema = ResultSchema.extend({\n  nextCursor: z.optional(z.string()),\n});\n\nconst ToolSchema = z\n  .object({\n    name: z.string(),\n    description: z.optional(z.string()),\n    inputSchema: z\n      .object({\n        type: z.literal('object'),\n        properties: z.optional(z.object({}).passthrough()),\n      })\n      .passthrough(),\n  })\n  .passthrough();\nexport type MCPTool = z.infer<typeof ToolSchema>;\nexport const ListToolsResultSchema = PaginatedResultSchema.extend({\n  tools: z.array(ToolSchema),\n});\nexport type ListToolsResult = z.infer<typeof ListToolsResultSchema>;\n\nconst TextContentSchema = z\n  .object({\n    type: z.literal('text'),\n    text: z.string(),\n  })\n  .passthrough();\nconst ImageContentSchema = z\n  .object({\n    type: z.literal('image'),\n    data: z.string().base64(),\n    mimeType: z.string(),\n  })\n  .passthrough();\nconst ResourceContentsSchema = z\n  .object({\n    /**\n     * The URI of this resource.\n     */\n    uri: z.string(),\n    /**\n     * The MIME type of this resource, if known.\n     */\n    mimeType: z.optional(z.string()),\n  })\n  .passthrough();\nconst TextResourceContentsSchema = ResourceContentsSchema.extend({\n  text: z.string(),\n});\nconst BlobResourceContentsSchema = ResourceContentsSchema.extend({\n  blob: z.string().base64(),\n});\nconst EmbeddedResourceSchema = z\n  .object({\n    type: z.literal('resource'),\n    resource: z.union([TextResourceContentsSchema, BlobResourceContentsSchema]),\n  })\n  .passthrough();\n\nexport const CallToolResultSchema = ResultSchema.extend({\n  content: z.array(\n    z.union([TextContentSchema, ImageContentSchema, EmbeddedResourceSchema]),\n  ),\n  isError: z.boolean().default(false).optional(),\n}).or(\n  ResultSchema.extend({\n    toolResult: z.unknown(),\n  }),\n);\nexport type CallToolResult = z.infer<typeof CallToolResultSchema>;\n", "export {\n  AISD<PERSON>rror,\n  APICallError,\n  EmptyResponseBodyError,\n  InvalidPromptError,\n  InvalidResponseDataError,\n  JSONParseError,\n  LoadAPIKeyError,\n  NoContentGeneratedError,\n  NoSuchModelError,\n  TypeValidationError,\n  UnsupportedFunctionalityError,\n} from '@ai-sdk/provider';\n\nexport { InvalidArgumentError } from './invalid-argument-error';\nexport { InvalidStreamPartError } from './invalid-stream-part-error';\nexport { InvalidToolArgumentsError } from './invalid-tool-arguments-error';\nexport { NoImageGeneratedError } from './no-image-generated-error';\nexport { NoObjectGeneratedError } from './no-object-generated-error';\nexport { NoOutputSpecifiedError } from './no-output-specified-error';\nexport { NoSuchToolError } from './no-such-tool-error';\nexport { ToolCallRepairError } from './tool-call-repair-error';\nexport { ToolExecutionError } from './tool-execution-error';\nexport { MCPClientError } from './mcp-client-error';\nexport { UnsupportedModelVersionError } from './unsupported-model-version-error';\n\nexport { InvalidDataContentError } from '../core/prompt/invalid-data-content-error';\nexport { InvalidMessageRoleError } from '../core/prompt/invalid-message-role-error';\nexport { MessageConversionError } from '../core/prompt/message-conversion-error';\nexport { DownloadError } from '../util/download-error';\nexport { RetryError } from '../util/retry-error';\n", "import { AISDKError } from '@ai-sdk/provider';\n\nconst name = 'AI_MCPClientError';\nconst marker = `vercel.ai.error.${name}`;\nconst symbol = Symbol.for(marker);\n\n/**\n * An error occurred with the MCP client.\n */\nexport class MCPClientError extends AISDKError {\n  private readonly [symbol] = true;\n\n  constructor({\n    name = 'MCPClientError',\n    message,\n    cause,\n  }: {\n    name?: string;\n    message: string;\n    cause?: unknown;\n  }) {\n    super({ name, message, cause });\n  }\n\n  static isInstance(error: unknown): error is MCPClientError {\n    return AISDKError.hasMarker(error, marker);\n  }\n}\n", "import { ChildProcess, spawn } from 'node:child_process';\nimport { getEnvironment } from './get-environment';\nimport { StdioConfig } from './mcp-stdio-transport';\n\nexport function createChildProcess(\n  config: StdioConfig,\n  signal: AbortSignal,\n): ChildProcess {\n  return spawn(config.command, config.args ?? [], {\n    env: getEnvironment(config.env),\n    stdio: ['pipe', 'pipe', config.stderr ?? 'inherit'],\n    shell: false,\n    signal,\n    windowsHide: globalThis.process.platform === 'win32' && isElectron(),\n    cwd: config.cwd,\n  });\n}\n\nfunction isElectron() {\n  return 'type' in globalThis.process;\n}\n", "/**\n * Constructs the environment variables for the child process.\n *\n * @param customEnv - Custom environment variables to merge with default environment variables.\n * @returns The environment variables for the child process.\n */\nexport function getEnvironment(\n  customEnv?: Record<string, string>,\n): Record<string, string> {\n  const DEFAULT_INHERITED_ENV_VARS =\n    globalThis.process.platform === 'win32'\n      ? [\n          'APPDATA',\n          'HOMEDRIVE',\n          'HOMEPATH',\n          'LOCALAPPDATA',\n          'PATH',\n          'PROCESSOR_ARCHITECTURE',\n          'SYSTEMDRIVE',\n          'SYSTEMROOT',\n          'TEMP',\n          'USERNAME',\n          'USERPROFILE',\n        ]\n      : ['HOME', 'LOGNAME', 'PATH', 'SHELL', 'TERM', 'USER'];\n\n  const env: Record<string, string> = customEnv ? { ...customEnv } : {};\n\n  for (const key of DEFAULT_INHERITED_ENV_VARS) {\n    const value = globalThis.process.env[key];\n    if (value === undefined) {\n      continue;\n    }\n\n    if (value.startsWith('()')) {\n      continue;\n    }\n\n    env[key] = value;\n  }\n\n  return env;\n}\n", "import type { ChildProcess, IOType } from 'node:child_process';\nimport { Stream } from 'node:stream';\nimport {\n  JSONRPCMessage,\n  JSONRPCMessageSchema,\n} from '../core/tool/mcp/json-rpc-message';\nimport { MCPTransport } from '../core/tool/mcp/mcp-transport';\nimport { MCPClientError } from '../errors';\nimport { createChildProcess } from './create-child-process';\n\nexport interface StdioConfig {\n  command: string;\n  args?: string[];\n  env?: Record<string, string>;\n  stderr?: IOType | Stream | number;\n  cwd?: string;\n}\n\nexport class StdioMCPTransport implements MCPTransport {\n  private process?: ChildProcess;\n  private abortController: AbortController = new AbortController();\n  private readBuffer: ReadBuffer = new ReadBuffer();\n  private serverParams: StdioConfig;\n\n  onclose?: () => void;\n  onerror?: (error: unknown) => void;\n  onmessage?: (message: JSONRPCMessage) => void;\n\n  constructor(server: StdioConfig) {\n    this.serverParams = server;\n  }\n\n  async start(): Promise<void> {\n    if (this.process) {\n      throw new MCPClientError({\n        message: 'StdioMCPTransport already started.',\n      });\n    }\n\n    return new Promise((resolve, reject) => {\n      try {\n        const process = createChildProcess(\n          this.serverParams,\n          this.abortController.signal,\n        );\n\n        this.process = process;\n\n        this.process.on('error', error => {\n          if (error.name === 'AbortError') {\n            this.onclose?.();\n            return;\n          }\n\n          reject(error);\n          this.onerror?.(error);\n        });\n\n        this.process.on('spawn', () => {\n          resolve();\n        });\n\n        this.process.on('close', _code => {\n          this.process = undefined;\n          this.onclose?.();\n        });\n\n        this.process.stdin?.on('error', error => {\n          this.onerror?.(error);\n        });\n\n        this.process.stdout?.on('data', chunk => {\n          this.readBuffer.append(chunk);\n          this.processReadBuffer();\n        });\n\n        this.process.stdout?.on('error', error => {\n          this.onerror?.(error);\n        });\n      } catch (error) {\n        reject(error);\n        this.onerror?.(error);\n      }\n    });\n  }\n\n  private processReadBuffer() {\n    while (true) {\n      try {\n        const message = this.readBuffer.readMessage();\n        if (message === null) {\n          break;\n        }\n\n        this.onmessage?.(message);\n      } catch (error) {\n        this.onerror?.(error as Error);\n      }\n    }\n  }\n\n  async close(): Promise<void> {\n    this.abortController.abort();\n    this.process = undefined;\n    this.readBuffer.clear();\n  }\n\n  send(message: JSONRPCMessage): Promise<void> {\n    return new Promise(resolve => {\n      if (!this.process?.stdin) {\n        throw new MCPClientError({\n          message: 'StdioClientTransport not connected',\n        });\n      }\n\n      const json = serializeMessage(message);\n      if (this.process.stdin.write(json)) {\n        resolve();\n      } else {\n        this.process.stdin.once('drain', resolve);\n      }\n    });\n  }\n}\n\nclass ReadBuffer {\n  private buffer?: Buffer;\n\n  append(chunk: Buffer): void {\n    this.buffer = this.buffer ? Buffer.concat([this.buffer, chunk]) : chunk;\n  }\n\n  readMessage(): JSONRPCMessage | null {\n    if (!this.buffer) return null;\n\n    const index = this.buffer.indexOf('\\n');\n    if (index === -1) {\n      return null;\n    }\n\n    const line = this.buffer.toString('utf8', 0, index);\n    this.buffer = this.buffer.subarray(index + 1);\n    return deserializeMessage(line);\n  }\n\n  clear(): void {\n    this.buffer = undefined;\n  }\n}\n\nfunction serializeMessage(message: JSONRPCMessage): string {\n  return JSON.stringify(message) + '\\n';\n}\n\nexport function deserializeMessage(line: string): JSONRPCMessage {\n  return JSONRPCMessageSchema.parse(JSON.parse(line));\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,IAAAA,cAAkB;;;ACAlB,iBAAkB;AAyClB,IAAM,qCAAqC,aACxC,OAAO;AAAA,EACN,MAAM,aAAE,OAAO;AAAA,EACf,SAAS,aAAE,OAAO;AACpB,CAAC,EACA,YAAY;AAGR,IAAM,mBAAmB,aAC7B,OAAO;AAAA,EACN,OAAO,aAAE,SAAS,aAAE,OAAO,CAAC,CAAC,EAAE,YAAY,CAAC;AAC9C,CAAC,EACA,YAAY;AAER,IAAM,eAAe;AAErB,IAAM,gBAAgB,aAAE,OAAO;AAAA,EACpC,QAAQ,aAAE,OAAO;AAAA,EACjB,QAAQ,aAAE,SAAS,gBAAgB;AACrC,CAAC;AAUD,IAAM,2BAA2B,aAC9B,OAAO;AAAA,EACN,cAAc,aAAE,SAAS,aAAE,OAAO,CAAC,CAAC,EAAE,YAAY,CAAC;AAAA,EACnD,SAAS,aAAE,SAAS,aAAE,OAAO,CAAC,CAAC,EAAE,YAAY,CAAC;AAAA,EAC9C,SAAS,aAAE;AAAA,IACT,aACG,OAAO;AAAA,MACN,aAAa,aAAE,SAAS,aAAE,QAAQ,CAAC;AAAA,IACrC,CAAC,EACA,YAAY;AAAA,EACjB;AAAA,EACA,WAAW,aAAE;AAAA,IACX,aACG,OAAO;AAAA,MACN,WAAW,aAAE,SAAS,aAAE,QAAQ,CAAC;AAAA,MACjC,aAAa,aAAE,SAAS,aAAE,QAAQ,CAAC;AAAA,IACrC,CAAC,EACA,YAAY;AAAA,EACjB;AAAA,EACA,OAAO,aAAE;AAAA,IACP,aACG,OAAO;AAAA,MACN,aAAa,aAAE,SAAS,aAAE,QAAQ,CAAC;AAAA,IACrC,CAAC,EACA,YAAY;AAAA,EACjB;AACF,CAAC,EACA,YAAY;AAGR,IAAM,yBAAyB,aAAa,OAAO;AAAA,EACxD,iBAAiB,aAAE,OAAO;AAAA,EAC1B,cAAc;AAAA,EACd,YAAY;AAAA,EACZ,cAAc,aAAE,SAAS,aAAE,OAAO,CAAC;AACrC,CAAC;AASD,IAAM,wBAAwB,aAAa,OAAO;AAAA,EAChD,YAAY,aAAE,SAAS,aAAE,OAAO,CAAC;AACnC,CAAC;AAED,IAAM,aAAa,aAChB,OAAO;AAAA,EACN,MAAM,aAAE,OAAO;AAAA,EACf,aAAa,aAAE,SAAS,aAAE,OAAO,CAAC;AAAA,EAClC,aAAa,aACV,OAAO;AAAA,IACN,MAAM,aAAE,QAAQ,QAAQ;AAAA,IACxB,YAAY,aAAE,SAAS,aAAE,OAAO,CAAC,CAAC,EAAE,YAAY,CAAC;AAAA,EACnD,CAAC,EACA,YAAY;AACjB,CAAC,EACA,YAAY;AAER,IAAM,wBAAwB,sBAAsB,OAAO;AAAA,EAChE,OAAO,aAAE,MAAM,UAAU;AAC3B,CAAC;AAGD,IAAM,oBAAoB,aACvB,OAAO;AAAA,EACN,MAAM,aAAE,QAAQ,MAAM;AAAA,EACtB,MAAM,aAAE,OAAO;AACjB,CAAC,EACA,YAAY;AACf,IAAM,qBAAqB,aACxB,OAAO;AAAA,EACN,MAAM,aAAE,QAAQ,OAAO;AAAA,EACvB,MAAM,aAAE,OAAO,EAAE,OAAO;AAAA,EACxB,UAAU,aAAE,OAAO;AACrB,CAAC,EACA,YAAY;AACf,IAAM,yBAAyB,aAC5B,OAAO;AAAA;AAAA;AAAA;AAAA,EAIN,KAAK,aAAE,OAAO;AAAA;AAAA;AAAA;AAAA,EAId,UAAU,aAAE,SAAS,aAAE,OAAO,CAAC;AACjC,CAAC,EACA,YAAY;AACf,IAAM,6BAA6B,uBAAuB,OAAO;AAAA,EAC/D,MAAM,aAAE,OAAO;AACjB,CAAC;AACD,IAAM,6BAA6B,uBAAuB,OAAO;AAAA,EAC/D,MAAM,aAAE,OAAO,EAAE,OAAO;AAC1B,CAAC;AACD,IAAM,yBAAyB,aAC5B,OAAO;AAAA,EACN,MAAM,aAAE,QAAQ,UAAU;AAAA,EAC1B,UAAU,aAAE,MAAM,CAAC,4BAA4B,0BAA0B,CAAC;AAC5E,CAAC,EACA,YAAY;AAER,IAAM,uBAAuB,aAAa,OAAO;AAAA,EACtD,SAAS,aAAE;AAAA,IACT,aAAE,MAAM,CAAC,mBAAmB,oBAAoB,sBAAsB,CAAC;AAAA,EACzE;AAAA,EACA,SAAS,aAAE,QAAQ,EAAE,QAAQ,KAAK,EAAE,SAAS;AAC/C,CAAC,EAAE;AAAA,EACD,aAAa,OAAO;AAAA,IAClB,YAAY,aAAE,QAAQ;AAAA,EACxB,CAAC;AACH;;;ADpLA,IAAM,kBAAkB;AAExB,IAAM,uBAAuB,cAC1B,OAAO;AAAA,EACN,SAAS,cAAE,QAAQ,eAAe;AAAA,EAClC,IAAI,cAAE,MAAM,CAAC,cAAE,OAAO,GAAG,cAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAC5C,CAAC,EACA,MAAM,aAAa,EACnB,OAAO;AAIV,IAAM,wBAAwB,cAC3B,OAAO;AAAA,EACN,SAAS,cAAE,QAAQ,eAAe;AAAA,EAClC,IAAI,cAAE,MAAM,CAAC,cAAE,OAAO,GAAG,cAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAAA,EAC1C,QAAQ;AACV,CAAC,EACA,OAAO;AAIV,IAAM,qBAAqB,cACxB,OAAO;AAAA,EACN,SAAS,cAAE,QAAQ,eAAe;AAAA,EAClC,IAAI,cAAE,MAAM,CAAC,cAAE,OAAO,GAAG,cAAE,OAAO,EAAE,IAAI,CAAC,CAAC;AAAA,EAC1C,OAAO,cAAE,OAAO;AAAA,IACd,MAAM,cAAE,OAAO,EAAE,IAAI;AAAA,IACrB,SAAS,cAAE,OAAO;AAAA,IAClB,MAAM,cAAE,SAAS,cAAE,QAAQ,CAAC;AAAA,EAC9B,CAAC;AACH,CAAC,EACA,OAAO;AAIV,IAAM,4BAA4B,cAC/B,OAAO;AAAA,EACN,SAAS,cAAE,QAAQ,eAAe;AACpC,CAAC,EACA;AAAA,EACC,cAAE,OAAO;AAAA,IACP,QAAQ,cAAE,OAAO;AAAA,IACjB,QAAQ,cAAE,SAAS,gBAAgB;AAAA,EACrC,CAAC;AACH,EACC,OAAO;AAIH,IAAM,uBAAuB,cAAE,MAAM;AAAA,EAC1C;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF,CAAC;;;AE1DD,IAAAC,mBAYO;;;ACZP,sBAA2B;AAE3B,IAAM,OAAO;AACb,IAAM,SAAS,mBAAmB,IAAI;AACtC,IAAM,SAAS,OAAO,IAAI,MAAM;AAJhC;AASO,IAAM,iBAAN,cAA6B,2BAAW;AAAA,EAG7C,YAAY;AAAA,IACV,MAAAC,QAAO;AAAA,IACP;AAAA,IACA;AAAA,EACF,GAIG;AACD,UAAM,EAAE,MAAAA,OAAM,SAAS,MAAM,CAAC;AAXhC,SAAkB,MAAU;AAAA,EAY5B;AAAA,EAEA,OAAO,WAAW,OAAyC;AACzD,WAAO,2BAAW,UAAU,OAAO,MAAM;AAAA,EAC3C;AACF;AAjBoB;;;ACVpB,gCAAoC;;;ACM7B,SAAS,eACd,WACwB;AACxB,QAAM,6BACJ,WAAW,QAAQ,aAAa,UAC5B;AAAA,IACE;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACF,IACA,CAAC,QAAQ,WAAW,QAAQ,SAAS,QAAQ,MAAM;AAEzD,QAAM,MAA8B,YAAY,EAAE,GAAG,UAAU,IAAI,CAAC;AAEpE,aAAW,OAAO,4BAA4B;AAC5C,UAAM,QAAQ,WAAW,QAAQ,IAAI,GAAG;AACxC,QAAI,UAAU,QAAW;AACvB;AAAA,IACF;AAEA,QAAI,MAAM,WAAW,IAAI,GAAG;AAC1B;AAAA,IACF;AAEA,QAAI,GAAG,IAAI;AAAA,EACb;AAEA,SAAO;AACT;;;ADtCO,SAAS,mBACd,QACA,QACc;AAPhB,MAAAC,KAAA;AAQE,aAAO,iCAAM,OAAO,UAASA,MAAA,OAAO,SAAP,OAAAA,MAAe,CAAC,GAAG;AAAA,IAC9C,KAAK,eAAe,OAAO,GAAG;AAAA,IAC9B,OAAO,CAAC,QAAQ,SAAQ,YAAO,WAAP,YAAiB,SAAS;AAAA,IAClD,OAAO;AAAA,IACP;AAAA,IACA,aAAa,WAAW,QAAQ,aAAa,WAAW,WAAW;AAAA,IACnE,KAAK,OAAO;AAAA,EACd,CAAC;AACH;AAEA,SAAS,aAAa;AACpB,SAAO,UAAU,WAAW;AAC9B;;;AEFO,IAAM,oBAAN,MAAgD;AAAA,EAUrD,YAAY,QAAqB;AARjC,SAAQ,kBAAmC,IAAI,gBAAgB;AAC/D,SAAQ,aAAyB,IAAI,WAAW;AAQ9C,SAAK,eAAe;AAAA,EACtB;AAAA,EAEA,MAAM,QAAuB;AAC3B,QAAI,KAAK,SAAS;AAChB,YAAM,IAAI,eAAe;AAAA,QACvB,SAAS;AAAA,MACX,CAAC;AAAA,IACH;AAEA,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAvC5C,UAAAC,KAAA;AAwCM,UAAI;AACF,cAAM,UAAU;AAAA,UACd,KAAK;AAAA,UACL,KAAK,gBAAgB;AAAA,QACvB;AAEA,aAAK,UAAU;AAEf,aAAK,QAAQ,GAAG,SAAS,WAAS;AAhD1C,cAAAA,KAAAC;AAiDU,cAAI,MAAM,SAAS,cAAc;AAC/B,aAAAD,MAAA,KAAK,YAAL,gBAAAA,IAAA;AACA;AAAA,UACF;AAEA,iBAAO,KAAK;AACZ,WAAAC,MAAA,KAAK,YAAL,gBAAAA,IAAA,WAAe;AAAA,QACjB,CAAC;AAED,aAAK,QAAQ,GAAG,SAAS,MAAM;AAC7B,kBAAQ;AAAA,QACV,CAAC;AAED,aAAK,QAAQ,GAAG,SAAS,WAAS;AA9D1C,cAAAD;AA+DU,eAAK,UAAU;AACf,WAAAA,MAAA,KAAK,YAAL,gBAAAA,IAAA;AAAA,QACF,CAAC;AAED,SAAAA,MAAA,KAAK,QAAQ,UAAb,gBAAAA,IAAoB,GAAG,SAAS,WAAS;AAnEjD,cAAAA;AAoEU,WAAAA,MAAA,KAAK,YAAL,gBAAAA,IAAA,WAAe;AAAA,QACjB;AAEA,mBAAK,QAAQ,WAAb,mBAAqB,GAAG,QAAQ,WAAS;AACvC,eAAK,WAAW,OAAO,KAAK;AAC5B,eAAK,kBAAkB;AAAA,QACzB;AAEA,mBAAK,QAAQ,WAAb,mBAAqB,GAAG,SAAS,WAAS;AA5ElD,cAAAA;AA6EU,WAAAA,MAAA,KAAK,YAAL,gBAAAA,IAAA,WAAe;AAAA,QACjB;AAAA,MACF,SAAS,OAAO;AACd,eAAO,KAAK;AACZ,mBAAK,YAAL,8BAAe;AAAA,MACjB;AAAA,IACF,CAAC;AAAA,EACH;AAAA,EAEQ,oBAAoB;AAtF9B,QAAAA,KAAA;AAuFI,WAAO,MAAM;AACX,UAAI;AACF,cAAM,UAAU,KAAK,WAAW,YAAY;AAC5C,YAAI,YAAY,MAAM;AACpB;AAAA,QACF;AAEA,SAAAA,MAAA,KAAK,cAAL,gBAAAA,IAAA,WAAiB;AAAA,MACnB,SAAS,OAAO;AACd,mBAAK,YAAL,8BAAe;AAAA,MACjB;AAAA,IACF;AAAA,EACF;AAAA,EAEA,MAAM,QAAuB;AAC3B,SAAK,gBAAgB,MAAM;AAC3B,SAAK,UAAU;AACf,SAAK,WAAW,MAAM;AAAA,EACxB;AAAA,EAEA,KAAK,SAAwC;AAC3C,WAAO,IAAI,QAAQ,aAAW;AA5GlC,UAAAA;AA6GM,UAAI,GAACA,MAAA,KAAK,YAAL,gBAAAA,IAAc,QAAO;AACxB,cAAM,IAAI,eAAe;AAAA,UACvB,SAAS;AAAA,QACX,CAAC;AAAA,MACH;AAEA,YAAM,OAAO,iBAAiB,OAAO;AACrC,UAAI,KAAK,QAAQ,MAAM,MAAM,IAAI,GAAG;AAClC,gBAAQ;AAAA,MACV,OAAO;AACL,aAAK,QAAQ,MAAM,KAAK,SAAS,OAAO;AAAA,MAC1C;AAAA,IACF,CAAC;AAAA,EACH;AACF;AAEA,IAAM,aAAN,MAAiB;AAAA,EAGf,OAAO,OAAqB;AAC1B,SAAK,SAAS,KAAK,SAAS,OAAO,OAAO,CAAC,KAAK,QAAQ,KAAK,CAAC,IAAI;AAAA,EACpE;AAAA,EAEA,cAAqC;AACnC,QAAI,CAAC,KAAK;AAAQ,aAAO;AAEzB,UAAM,QAAQ,KAAK,OAAO,QAAQ,IAAI;AACtC,QAAI,UAAU,IAAI;AAChB,aAAO;AAAA,IACT;AAEA,UAAM,OAAO,KAAK,OAAO,SAAS,QAAQ,GAAG,KAAK;AAClD,SAAK,SAAS,KAAK,OAAO,SAAS,QAAQ,CAAC;AAC5C,WAAO,mBAAmB,IAAI;AAAA,EAChC;AAAA,EAEA,QAAc;AACZ,SAAK,SAAS;AAAA,EAChB;AACF;AAEA,SAAS,iBAAiB,SAAiC;AACzD,SAAO,KAAK,UAAU,OAAO,IAAI;AACnC;AAEO,SAAS,mBAAmB,MAA8B;AAC/D,SAAO,qBAAqB,MAAM,KAAK,MAAM,IAAI,CAAC;AACpD;", "names": ["import_zod", "import_provider", "name", "_a", "_a", "_b"]}