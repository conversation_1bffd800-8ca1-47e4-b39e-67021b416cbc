{"name": "swr", "version": "2.3.4", "description": "React Hooks library for remote data fetching", "keywords": ["swr", "react", "hooks", "request", "cache", "fetch"], "packageManager": "pnpm@8.4.0", "main": "./dist/index/index.js", "module": "./dist/index/index.mjs", "types": "./dist/index/index.d.ts", "sideEffects": false, "exports": {"./package.json": "./package.json", ".": {"react-server": "./dist/index/react-server.mjs", "import": {"types": "./dist/index/index.d.mts", "default": "./dist/index/index.mjs"}, "require": {"types": "./dist/index/index.d.ts", "default": "./dist/index/index.js"}}, "./infinite": {"react-server": "./dist/infinite/react-server.mjs", "import": {"types": "./dist/infinite/index.d.mts", "default": "./dist/infinite/index.mjs"}, "require": {"types": "./dist/infinite/index.d.ts", "default": "./dist/infinite/index.js"}}, "./immutable": {"import": {"types": "./dist/immutable/index.d.mts", "default": "./dist/immutable/index.mjs"}, "require": {"types": "./dist/immutable/index.d.ts", "default": "./dist/immutable/index.js"}}, "./subscription": {"import": {"types": "./dist/subscription/index.d.mts", "default": "./dist/subscription/index.mjs"}, "require": {"types": "./dist/subscription/index.d.ts", "default": "./dist/subscription/index.js"}}, "./mutation": {"import": {"types": "./dist/mutation/index.d.mts", "default": "./dist/mutation/index.mjs"}, "require": {"types": "./dist/mutation/index.d.ts", "default": "./dist/mutation/index.js"}}, "./_internal": {"react-server": "./dist/_internal/react-server.mjs", "import": {"types": "./dist/_internal/index.d.mts", "default": "./dist/_internal/index.mjs"}, "require": {"types": "./dist/_internal/index.d.ts", "default": "./dist/_internal/index.js"}}}, "files": ["dist", "infinite", "immutable", "subscription", "mutation", "_internal"], "repository": "github:vercel/swr", "homepage": "https://swr.vercel.app", "license": "MIT", "scripts": {"prepare": "husky install", "csb:install": "corepack enable && corepack pnpm i", "csb:build": "pnpm build", "clean": "rimraf ./dist && rimraf playwright-report test-result", "watch": "bunchee -w", "build": "bunchee", "build:e2e": "pnpm next build e2e/site", "attw": "attw --pack .", "types:check": "tsc --noEmit", "prepublishOnly": "pnpm clean && pnpm build", "publish-beta": "pnpm publish --tag beta", "format": "prettier --write ./**/*.{ts,tsx}", "lint": "eslint . --ext .ts,.tsx --cache", "lint:fix": "pnpm lint --fix", "coverage": "jest --coverage", "test-typing": "tsc -p test/tsconfig.json && tsc -p test/type/tsconfig.json && tsc -p test/type/suspense/tsconfig.json", "test": "jest", "test:build": "jest --config jest.config.build.js", "test:e2e": "playwright test", "run-all-checks": "pnpm types:check && pnpm lint && pnpm test-typing"}, "lint-staged": {"*.{ts,tsx}": ["eslint --fix --cache", "prettier --write"]}, "devDependencies": {"@edge-runtime/jest-environment": "^3.0.4", "@arethetypeswrong/cli": "^0.15.3", "@playwright/test": "1.34.3", "@swc/core": "^1.3.62", "@swc/jest": "0.2.26", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^14.2.1", "@type-challenges/utils": "0.1.1", "@types/jest": "^29.5.2", "@types/node": "^20.2.5", "@types/react": "^18.2.8", "@types/use-sync-external-store": "^0.0.3", "@typescript-eslint/eslint-plugin": "5.59.8", "@typescript-eslint/parser": "5.59.8", "bunchee": "^6.3.2", "eslint": "8.42.0", "eslint-config-prettier": "8.8.0", "eslint-plugin-jest-dom": "5.1.0", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "eslint-plugin-testing-library": "5.11.0", "husky": "8.0.3", "jest": "29.7.0", "jest-environment-jsdom": "29.7.0", "lint-staged": "13.2.2", "next": "15.0.4", "prettier": "2.8.8", "react": "^18.2.0", "react-dom": "^18.2.0", "react-error-boundary": "^4.0.9", "rimraf": "5.0.1", "semver": "^7.5.1", "swr": "workspace:*", "typescript": "5.1.3"}, "peerDependencies": {"react": "^16.11.0 || ^17.0.0 || ^18.0.0 || ^19.0.0"}, "prettier": {"tabWidth": 2, "semi": false, "useTabs": false, "singleQuote": true, "arrowParens": "avoid", "trailingComma": "none"}, "dependencies": {"dequal": "^2.0.3", "use-sync-external-store": "^1.4.0"}}