import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

// 系统健康状态接口
interface SystemHealth {
  status: 'healthy' | 'warning' | 'critical';
  uptime: number;
  memoryUsage: number;
  cpuUsage: number;
  diskUsage: number;
  activeConnections: number;
  databaseStatus: 'connected' | 'disconnected' | 'error';
  services: {
    database: boolean;
    redis: boolean;
    ai: boolean;
    storage: boolean;
  };
  metrics: {
    requestsPerMinute: number;
    averageResponseTime: number;
    errorRate: number;
  };
  timestamp: string;
}

// 验证管理员权限
function verifyAdminAccess(request: NextRequest): boolean {
  const authHeader = request.headers.get('authorization');
  const adminToken = process.env.ADMIN_API_TOKEN;
  
  if (!adminToken || !authHeader) {
    return false;
  }
  
  return authHeader === `Bearer ${adminToken}`;
}

// 检查数据库连接
async function checkDatabaseHealth(): Promise<{ status: 'connected' | 'disconnected' | 'error'; responseTime: number }> {
  const startTime = Date.now();
  
  try {
    await prisma.$queryRaw`SELECT 1`;
    const responseTime = Date.now() - startTime;
    return { status: 'connected', responseTime };
  } catch (error) {
    console.error('Database health check failed:', error);
    return { status: 'error', responseTime: Date.now() - startTime };
  }
}

// 检查Redis连接（如果使用）
async function checkRedisHealth(): Promise<boolean> {
  try {
    // 这里应该实现Redis连接检查
    // const redis = getRedisClient();
    // await redis.ping();
    return true;
  } catch (error) {
    console.error('Redis health check failed:', error);
    return false;
  }
}

// 检查AI服务状态
async function checkAIServicesHealth(): Promise<boolean> {
  try {
    // 检查AI服务管理器状态
    const response = await fetch(`${process.env.NEXT_PUBLIC_SITE_URL}/api/ai/mystical`, {
      method: 'GET',
    });
    return response.ok;
  } catch (error) {
    console.error('AI services health check failed:', error);
    return false;
  }
}

// 获取系统指标
function getSystemMetrics(): {
  memoryUsage: number;
  cpuUsage: number;
  diskUsage: number;
  uptime: number;
} {
  // 在Node.js环境中获取系统指标
  const memoryUsage = process.memoryUsage();
  const totalMemory = memoryUsage.heapTotal;
  const usedMemory = memoryUsage.heapUsed;
  const memoryUsagePercent = (usedMemory / totalMemory) * 100;

  // 进程运行时间
  const uptime = process.uptime();

  // CPU和磁盘使用率（简化版本，实际应该使用系统监控库）
  const cpuUsage = Math.random() * 30 + 10; // 模拟10-40%
  const diskUsage = Math.random() * 20 + 30; // 模拟30-50%

  return {
    memoryUsage: memoryUsagePercent,
    cpuUsage,
    diskUsage,
    uptime,
  };
}

// 获取请求指标
async function getRequestMetrics(): Promise<{
  requestsPerMinute: number;
  averageResponseTime: number;
  errorRate: number;
}> {
  try {
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000);
    
    // 获取最近一分钟的请求数
    const recentRequests = await prisma.analytics.count({
      where: {
        timestamp: { gte: oneMinuteAgo }
      }
    });

    // 获取错误率
    const recentErrors = await prisma.analytics.count({
      where: {
        event: { startsWith: 'error' },
        timestamp: { gte: oneMinuteAgo }
      }
    });

    const errorRate = recentRequests > 0 ? (recentErrors / recentRequests) * 100 : 0;

    // 模拟平均响应时间
    const averageResponseTime = Math.random() * 100 + 50; // 50-150ms

    return {
      requestsPerMinute: recentRequests,
      averageResponseTime: Math.round(averageResponseTime),
      errorRate: Math.round(errorRate * 100) / 100,
    };
  } catch (error) {
    console.error('Failed to get request metrics:', error);
    return {
      requestsPerMinute: 0,
      averageResponseTime: 0,
      errorRate: 0,
    };
  }
}

// 确定整体健康状态
function determineOverallStatus(
  databaseStatus: string,
  services: any,
  metrics: any,
  systemMetrics: any
): 'healthy' | 'warning' | 'critical' {
  // 关键服务检查
  if (databaseStatus === 'error' || !services.database) {
    return 'critical';
  }

  // 性能指标检查
  if (
    systemMetrics.memoryUsage > 90 ||
    systemMetrics.cpuUsage > 80 ||
    metrics.errorRate > 5
  ) {
    return 'critical';
  }

  // 警告条件
  if (
    systemMetrics.memoryUsage > 70 ||
    systemMetrics.cpuUsage > 60 ||
    metrics.errorRate > 1 ||
    metrics.averageResponseTime > 1000
  ) {
    return 'warning';
  }

  return 'healthy';
}

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // 验证管理员权限（在生产环境中）
    if (process.env.NODE_ENV === 'production' && !verifyAdminAccess(request)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // 并行执行所有健康检查
    const [
      databaseHealth,
      redisHealth,
      aiHealth,
      systemMetrics,
      requestMetrics
    ] = await Promise.all([
      checkDatabaseHealth(),
      checkRedisHealth(),
      checkAIServicesHealth(),
      Promise.resolve(getSystemMetrics()),
      getRequestMetrics()
    ]);

    // 服务状态
    const services = {
      database: databaseHealth.status === 'connected',
      redis: redisHealth,
      ai: aiHealth,
      storage: true, // 假设存储服务正常
    };

    // 活跃连接数（模拟）
    const activeConnections = Math.floor(Math.random() * 100) + 50;

    // 确定整体状态
    const overallStatus = determineOverallStatus(
      databaseHealth.status,
      services,
      requestMetrics,
      systemMetrics
    );

    const healthData: SystemHealth = {
      status: overallStatus,
      uptime: systemMetrics.uptime,
      memoryUsage: Math.round(systemMetrics.memoryUsage * 100) / 100,
      cpuUsage: Math.round(systemMetrics.cpuUsage * 100) / 100,
      diskUsage: Math.round(systemMetrics.diskUsage * 100) / 100,
      activeConnections,
      databaseStatus: databaseHealth.status,
      services,
      metrics: requestMetrics,
      timestamp: new Date().toISOString(),
    };

    // 设置适当的缓存头
    const response = NextResponse.json(healthData);
    response.headers.set('Cache-Control', 'no-cache, no-store, must-revalidate');
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    return response;

  } catch (error) {
    console.error('Health check API error:', error);
    
    // 返回错误状态
    const errorHealth: SystemHealth = {
      status: 'critical',
      uptime: process.uptime(),
      memoryUsage: 0,
      cpuUsage: 0,
      diskUsage: 0,
      activeConnections: 0,
      databaseStatus: 'error',
      services: {
        database: false,
        redis: false,
        ai: false,
        storage: false,
      },
      metrics: {
        requestsPerMinute: 0,
        averageResponseTime: 0,
        errorRate: 100,
      },
      timestamp: new Date().toISOString(),
    };

    return NextResponse.json(errorHealth, { status: 500 });
  }
}

// 健康检查的简化版本（用于负载均衡器等）
export async function HEAD(request: NextRequest): Promise<NextResponse> {
  try {
    // 快速数据库检查
    await prisma.$queryRaw`SELECT 1`;
    
    return new NextResponse(null, { 
      status: 200,
      headers: {
        'Cache-Control': 'no-cache',
      }
    });
  } catch (error) {
    return new NextResponse(null, { status: 503 });
  }
}
