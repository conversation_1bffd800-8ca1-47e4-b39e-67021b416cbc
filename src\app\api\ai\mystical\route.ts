import { NextRequest, NextResponse } from 'next/server';
import { aiServiceManager } from '@/lib/ai/ai-service-manager';
import { PromptManager } from '@/lib/ai/mystical-prompts';
import type { Locale } from '@/types';

// 支持的AI服务类型
type AIServiceType = 'tarot' | 'astrology' | 'numerology' | 'crystal' | 'general';

interface AIRequest {
  type: AIServiceType;
  promptId: string;
  variables: Record<string, string>;
  locale: Locale;
  userId?: string;
}

interface AIResponse {
  success: boolean;
  content?: string;
  error?: string;
  provider?: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  responseTime?: number;
}

export async function POST(request: NextRequest): Promise<NextResponse<AIResponse>> {
  try {
    // 解析请求体
    const body: AIRequest = await request.json();
    const { type, promptId, variables, locale, userId } = body;

    // 验证请求参数
    if (!type || !promptId || !variables || !locale) {
      return NextResponse.json({
        success: false,
        error: '缺少必要参数：type, promptId, variables, locale',
      }, { status: 400 });
    }

    // 验证语言支持
    const supportedLocales: Locale[] = ['en', 'zh', 'es', 'pt', 'hi', 'ja', 'de', 'fr', 'it', 'ru', 'ko', 'ar'];
    if (!supportedLocales.includes(locale)) {
      return NextResponse.json({
        success: false,
        error: `不支持的语言: ${locale}`,
      }, { status: 400 });
    }

    // 获取提示词模板
    const promptTemplate = PromptManager.getPromptById(promptId, locale);
    if (!promptTemplate) {
      return NextResponse.json({
        success: false,
        error: `未找到提示词模板: ${promptId}`,
      }, { status: 404 });
    }

    // 验证变量完整性
    const missingVariables = PromptManager.validatePromptVariables(promptTemplate, variables);
    if (missingVariables.length > 0) {
      return NextResponse.json({
        success: false,
        error: `缺少必要变量: ${missingVariables.join(', ')}`,
      }, { status: 400 });
    }

    // 填充提示词模板
    const userPrompt = PromptManager.fillPromptTemplate(promptTemplate, variables);

    // 记录请求日志
    console.log(`AI请求 - 类型: ${type}, 提示词: ${promptId}, 语言: ${locale}, 用户: ${userId || '匿名'}`);

    // 调用AI服务
    const startTime = Date.now();
    const aiResponse = await aiServiceManager.generateText({
      prompt: userPrompt,
      systemPrompt: promptTemplate.systemPrompt,
      maxTokens: 2000,
      temperature: 0.7,
    });

    const responseTime = Date.now() - startTime;

    // 记录成功日志
    console.log(`AI响应成功 - 提供商: ${aiResponse.provider}, 响应时间: ${responseTime}ms, 令牌使用: ${aiResponse.usage.totalTokens}`);

    // 可选：记录到数据库用于分析
    if (userId) {
      await logAIUsage({
        userId,
        type,
        promptId,
        locale,
        provider: aiResponse.provider,
        tokensUsed: aiResponse.usage.totalTokens,
        responseTime,
        success: true,
      });
    }

    return NextResponse.json({
      success: true,
      content: aiResponse.content,
      provider: aiResponse.provider,
      usage: aiResponse.usage,
      responseTime,
    });

  } catch (error) {
    console.error('AI服务错误:', error);

    // 记录错误日志
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    
    return NextResponse.json({
      success: false,
      error: `AI服务暂时不可用: ${errorMessage}`,
    }, { status: 500 });
  }
}

// 获取AI服务状态
export async function GET(): Promise<NextResponse> {
  try {
    const status = aiServiceManager.getProviderStatus();
    
    return NextResponse.json({
      success: true,
      providers: status,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('获取AI服务状态失败:', error);
    
    return NextResponse.json({
      success: false,
      error: '无法获取服务状态',
    }, { status: 500 });
  }
}

// 记录AI使用情况（可选功能）
async function logAIUsage(data: {
  userId: string;
  type: string;
  promptId: string;
  locale: string;
  provider: string;
  tokensUsed: number;
  responseTime: number;
  success: boolean;
}) {
  try {
    // 这里可以记录到数据库或分析服务
    // 例如：await prisma.aiUsageLog.create({ data });
    console.log('AI使用记录:', data);
  } catch (error) {
    console.error('记录AI使用情况失败:', error);
    // 不抛出错误，避免影响主要功能
  }
}

// 速率限制中间件（可选）
function checkRateLimit(userId: string): boolean {
  // 实现速率限制逻辑
  // 例如：每分钟最多10次请求
  return true; // 暂时返回true
}

// 内容过滤中间件（可选）
function filterContent(content: string): string {
  // 实现内容过滤逻辑
  // 例如：移除敏感信息、格式化输出等
  return content;
}
