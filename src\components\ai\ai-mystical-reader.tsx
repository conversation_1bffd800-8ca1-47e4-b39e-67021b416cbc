'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Loader2, <PERSON>rkles, AlertCircle } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { Locale } from '@/types';

interface AIMysticalReaderProps {
  type: 'tarot' | 'astrology' | 'numerology' | 'crystal' | 'general';
  promptId: string;
  locale: Locale;
  variables: Record<string, string>;
  title?: string;
  description?: string;
  className?: string;
}

interface AIResponse {
  success: boolean;
  content?: string;
  error?: string;
  provider?: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  responseTime?: number;
}

export function AIMysticalReader({
  type,
  promptId,
  locale,
  variables,
  title,
  description,
  className = '',
}: AIMysticalReaderProps) {
  const t = useTranslations('ai');
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<AIResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleGenerate = async () => {
    setIsLoading(true);
    setError(null);
    setResponse(null);

    try {
      const res = await fetch('/api/ai/mystical', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          type,
          promptId,
          variables,
          locale,
        }),
      });

      const data: AIResponse = await res.json();

      if (data.success) {
        setResponse(data);
      } else {
        setError(data.error || t('error.unknown'));
      }
    } catch (err) {
      console.error('AI请求失败:', err);
      setError(t('error.network'));
    } finally {
      setIsLoading(false);
    }
  };

  const formatContent = (content: string) => {
    // 将AI响应格式化为更好的显示格式
    return content
      .split('\n')
      .map((line, index) => {
        if (line.trim() === '') return null;
        
        // 检测标题行（以##开头）
        if (line.startsWith('##')) {
          return (
            <h3 key={index} className="text-lg font-semibold text-mystical-700 dark:text-mystical-300 mt-4 mb-2">
              {line.replace('##', '').trim()}
            </h3>
          );
        }
        
        // 检测列表项（以-或*开头）
        if (line.trim().startsWith('-') || line.trim().startsWith('*')) {
          return (
            <li key={index} className="ml-4 mb-1 text-mystical-600 dark:text-mystical-400">
              {line.replace(/^[-*]\s*/, '')}
            </li>
          );
        }
        
        // 普通段落
        return (
          <p key={index} className="mb-3 text-mystical-700 dark:text-mystical-300 leading-relaxed">
            {line}
          </p>
        );
      })
      .filter(Boolean);
  };

  return (
    <Card className={`w-full ${className}`}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-mystical-500" />
          {title || t('title')}
        </CardTitle>
        {description && (
          <p className="text-sm text-mystical-600 dark:text-mystical-400">
            {description}
          </p>
        )}
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* 生成按钮 */}
        <Button
          onClick={handleGenerate}
          disabled={isLoading}
          className="w-full bg-gradient-to-r from-mystical-500 to-mystical-600 hover:from-mystical-600 hover:to-mystical-700"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              {t('generating')}
            </>
          ) : (
            <>
              <Sparkles className="mr-2 h-4 w-4" />
              {t('generate')}
            </>
          )}
        </Button>

        {/* 错误显示 */}
        {error && (
          <div className="flex items-center gap-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <AlertCircle className="h-4 w-4 text-red-500" />
            <span className="text-sm text-red-700 dark:text-red-300">{error}</span>
          </div>
        )}

        {/* AI响应显示 */}
        {response && response.content && (
          <div className="space-y-4">
            {/* 响应内容 */}
            <div className="p-4 bg-gradient-to-br from-mystical-50 to-purple-50 dark:from-mystical-900/20 dark:to-purple-900/20 border border-mystical-200 dark:border-mystical-700 rounded-lg">
              <div className="prose prose-mystical dark:prose-invert max-w-none">
                {formatContent(response.content)}
              </div>
            </div>

            {/* 元数据 */}
            <div className="flex flex-wrap gap-2 text-xs text-mystical-500 dark:text-mystical-400">
              {response.provider && (
                <Badge variant="outline" className="text-xs">
                  {t('provider')}: {response.provider}
                </Badge>
              )}
              {response.responseTime && (
                <Badge variant="outline" className="text-xs">
                  {t('responseTime')}: {response.responseTime}ms
                </Badge>
              )}
              {response.usage && (
                <Badge variant="outline" className="text-xs">
                  {t('tokens')}: {response.usage.totalTokens}
                </Badge>
              )}
            </div>
          </div>
        )}

        {/* 使用提示 */}
        <div className="text-xs text-mystical-500 dark:text-mystical-400 border-t border-mystical-200 dark:border-mystical-700 pt-3">
          <p>{t('disclaimer')}</p>
        </div>
      </CardContent>
    </Card>
  );
}

// 预设的AI读卡器组件
export function TarotAIReader({
  cardName,
  orientation,
  question,
  background,
  locale,
  className,
}: {
  cardName: string;
  orientation: 'upright' | 'reversed';
  question: string;
  background: string;
  locale: Locale;
  className?: string;
}) {
  const t = useTranslations('tarot');

  return (
    <AIMysticalReader
      type="tarot"
      promptId="tarot-single-card"
      locale={locale}
      variables={{
        cardName,
        orientation: orientation === 'upright' ? t('upright') : t('reversed'),
        question,
        background,
      }}
      title={t('ai.title')}
      description={t('ai.description')}
      className={className}
    />
  );
}

export function AstrologyAIReader({
  birthDate,
  birthTime,
  birthPlace,
  sunSign,
  moonSign,
  ascendant,
  locale,
  className,
}: {
  birthDate: string;
  birthTime: string;
  birthPlace: string;
  sunSign: string;
  moonSign: string;
  ascendant: string;
  locale: Locale;
  className?: string;
}) {
  const t = useTranslations('astrology');

  return (
    <AIMysticalReader
      type="astrology"
      promptId="natal-chart-analysis"
      locale={locale}
      variables={{
        birthDate,
        birthTime,
        birthPlace,
        sunSign,
        moonSign,
        ascendant,
        planetPositions: '', // 可以从星盘计算中获取
        aspects: '', // 可以从星盘计算中获取
      }}
      title={t('ai.title')}
      description={t('ai.description')}
      className={className}
    />
  );
}

export function NumerologyAIReader({
  lifePathNumber,
  birthDate,
  fullName,
  locale,
  className,
}: {
  lifePathNumber: number;
  birthDate: string;
  fullName: string;
  locale: Locale;
  className?: string;
}) {
  const t = useTranslations('numerology');

  return (
    <AIMysticalReader
      type="numerology"
      promptId="life-path-number"
      locale={locale}
      variables={{
        lifePathNumber: lifePathNumber.toString(),
        birthDate,
        fullName,
        otherNumbers: '', // 可以计算其他重要数字
      }}
      title={t('ai.title')}
      description={t('ai.description')}
      className={className}
    />
  );
}
