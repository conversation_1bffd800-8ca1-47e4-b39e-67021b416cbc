'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { AstrologyAIReader } from '@/components/ai/ai-mystical-reader';
import { Star, MapPin, Clock } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { Locale } from '@/types';

// 星座数据
const ZODIAC_SIGNS = [
  { id: 'aries', name: 'Aries', nameZh: '白羊座', symbol: '♈', element: 'fire', quality: 'cardinal' },
  { id: 'taurus', name: 'Taurus', nameZh: '金牛座', symbol: '♉', element: 'earth', quality: 'fixed' },
  { id: 'gemini', name: 'Gemini', nameZh: '双子座', symbol: '♊', element: 'air', quality: 'mutable' },
  { id: 'cancer', name: 'Cancer', nameZh: '巨蟹座', symbol: '♋', element: 'water', quality: 'cardinal' },
  { id: 'leo', name: 'Leo', nameZh: '狮子座', symbol: '♌', element: 'fire', quality: 'fixed' },
  { id: 'virgo', name: 'Virgo', nameZh: '处女座', symbol: '♍', element: 'earth', quality: 'mutable' },
  { id: 'libra', name: 'Libra', nameZh: '天秤座', symbol: '♎', element: 'air', quality: 'cardinal' },
  { id: 'scorpio', name: 'Scorpio', nameZh: '天蝎座', symbol: '♏', element: 'water', quality: 'fixed' },
  { id: 'sagittarius', name: 'Sagittarius', nameZh: '射手座', symbol: '♐', element: 'fire', quality: 'mutable' },
  { id: 'capricorn', name: 'Capricorn', nameZh: '摩羯座', symbol: '♑', element: 'earth', quality: 'cardinal' },
  { id: 'aquarius', name: 'Aquarius', nameZh: '水瓶座', symbol: '♒', element: 'air', quality: 'fixed' },
  { id: 'pisces', name: 'Pisces', nameZh: '双鱼座', symbol: '♓', element: 'water', quality: 'mutable' },
];

interface NatalChartData {
  birthDate: string;
  birthTime: string;
  birthPlace: string;
  sunSign: string;
  moonSign: string;
  ascendant: string;
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

interface NatalChartCalculatorProps {
  locale: Locale;
  className?: string;
}

export function NatalChartCalculator({ locale, className = '' }: NatalChartCalculatorProps) {
  const t = useTranslations('natal-chart');
  const [formData, setFormData] = useState({
    birthDate: '',
    birthTime: '',
    birthPlace: '',
  });
  const [chartData, setChartData] = useState<NatalChartData | null>(null);
  const [showAIReading, setShowAIReading] = useState(false);
  const [isCalculating, setIsCalculating] = useState(false);

  // 简化的星座计算（实际应用中需要使用专业的天文计算库）
  const calculateSunSign = (birthDate: string): string => {
    const date = new Date(birthDate);
    const month = date.getMonth() + 1;
    const day = date.getDate();

    // 简化的星座日期范围
    if ((month === 3 && day >= 21) || (month === 4 && day <= 19)) return 'aries';
    if ((month === 4 && day >= 20) || (month === 5 && day <= 20)) return 'taurus';
    if ((month === 5 && day >= 21) || (month === 6 && day <= 20)) return 'gemini';
    if ((month === 6 && day >= 21) || (month === 7 && day <= 22)) return 'cancer';
    if ((month === 7 && day >= 23) || (month === 8 && day <= 22)) return 'leo';
    if ((month === 8 && day >= 23) || (month === 9 && day <= 22)) return 'virgo';
    if ((month === 9 && day >= 23) || (month === 10 && day <= 22)) return 'libra';
    if ((month === 10 && day >= 23) || (month === 11 && day <= 21)) return 'scorpio';
    if ((month === 11 && day >= 22) || (month === 12 && day <= 21)) return 'sagittarius';
    if ((month === 12 && day >= 22) || (month === 1 && day <= 19)) return 'capricorn';
    if ((month === 1 && day >= 20) || (month === 2 && day <= 18)) return 'aquarius';
    if ((month === 2 && day >= 19) || (month === 3 && day <= 20)) return 'pisces';
    
    return 'aries'; // 默认值
  };

  // 模拟月亮星座计算（实际需要复杂的天文计算）
  const calculateMoonSign = (birthDate: string, birthTime: string): string => {
    // 这里是简化版本，实际应用需要使用专业的天文计算
    const signs = ZODIAC_SIGNS.map(s => s.id);
    const hash = (birthDate + birthTime).split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    return signs[Math.abs(hash) % signs.length];
  };

  // 模拟上升星座计算
  const calculateAscendant = (birthDate: string, birthTime: string, birthPlace: string): string => {
    // 这里是简化版本，实际应用需要使用专业的天文计算
    const signs = ZODIAC_SIGNS.map(s => s.id);
    const hash = (birthDate + birthTime + birthPlace).split('').reduce((a, b) => {
      a = ((a << 5) - a) + b.charCodeAt(0);
      return a & a;
    }, 0);
    return signs[Math.abs(hash) % signs.length];
  };

  // 计算星盘
  const calculateChart = async () => {
    if (!formData.birthDate || !formData.birthTime || !formData.birthPlace) return;

    setIsCalculating(true);

    try {
      // 模拟计算延迟
      await new Promise(resolve => setTimeout(resolve, 2000));

      const sunSign = calculateSunSign(formData.birthDate);
      const moonSign = calculateMoonSign(formData.birthDate, formData.birthTime);
      const ascendant = calculateAscendant(formData.birthDate, formData.birthTime, formData.birthPlace);

      const chartData: NatalChartData = {
        birthDate: formData.birthDate,
        birthTime: formData.birthTime,
        birthPlace: formData.birthPlace,
        sunSign,
        moonSign,
        ascendant,
      };

      setChartData(chartData);
      setShowAIReading(false);
    } catch (error) {
      console.error('星盘计算失败:', error);
    } finally {
      setIsCalculating(false);
    }
  };

  // 重置
  const reset = () => {
    setFormData({ birthDate: '', birthTime: '', birthPlace: '' });
    setChartData(null);
    setShowAIReading(false);
  };

  // 获取星座信息
  const getZodiacInfo = (signId: string) => {
    const sign = ZODIAC_SIGNS.find(s => s.id === signId);
    if (!sign) return null;

    return {
      name: locale === 'zh' ? sign.nameZh : sign.name,
      symbol: sign.symbol,
      element: sign.element,
      quality: sign.quality,
    };
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 输入表单 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Star className="h-5 w-5 text-mystical-500" />
            {t('calculator.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 出生日期 */}
          <div className="space-y-2">
            <Label htmlFor="birthDate" className="flex items-center gap-2">
              <Star className="h-4 w-4" />
              {t('input.birthDate')}
            </Label>
            <Input
              id="birthDate"
              type="date"
              value={formData.birthDate}
              onChange={(e) => setFormData(prev => ({ ...prev, birthDate: e.target.value }))}
            />
          </div>

          {/* 出生时间 */}
          <div className="space-y-2">
            <Label htmlFor="birthTime" className="flex items-center gap-2">
              <Clock className="h-4 w-4" />
              {t('input.birthTime')}
            </Label>
            <Input
              id="birthTime"
              type="time"
              value={formData.birthTime}
              onChange={(e) => setFormData(prev => ({ ...prev, birthTime: e.target.value }))}
            />
          </div>

          {/* 出生地点 */}
          <div className="space-y-2">
            <Label htmlFor="birthPlace" className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              {t('input.birthPlace')}
            </Label>
            <Input
              id="birthPlace"
              type="text"
              placeholder={t('input.birthPlacePlaceholder')}
              value={formData.birthPlace}
              onChange={(e) => setFormData(prev => ({ ...prev, birthPlace: e.target.value }))}
            />
          </div>

          {/* 计算按钮 */}
          <div className="flex gap-2">
            <Button
              onClick={calculateChart}
              disabled={!formData.birthDate || !formData.birthTime || !formData.birthPlace || isCalculating}
              className="flex-1 bg-gradient-to-r from-mystical-500 to-mystical-600 hover:from-mystical-600 hover:to-mystical-700"
            >
              {isCalculating ? (
                <>
                  <Star className="mr-2 h-4 w-4 animate-spin" />
                  {t('calculating')}
                </>
              ) : (
                <>
                  <Star className="mr-2 h-4 w-4" />
                  {t('calculate')}
                </>
              )}
            </Button>
            
            {chartData && (
              <Button onClick={reset} variant="outline">
                {t('reset')}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 星盘结果 */}
      {chartData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Star className="h-5 w-5 text-mystical-500" />
              {t('results.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 核心三要素 */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 太阳星座 */}
              <div className="text-center p-6 bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 rounded-lg border border-yellow-200 dark:border-yellow-700">
                <div className="text-4xl mb-2">{getZodiacInfo(chartData.sunSign)?.symbol}</div>
                <div className="text-lg font-semibold text-yellow-700 dark:text-yellow-300 mb-1">
                  {t('signs.sun')}
                </div>
                <div className="text-sm font-medium text-yellow-600 dark:text-yellow-400">
                  {getZodiacInfo(chartData.sunSign)?.name}
                </div>
                <Badge variant="outline" className="mt-2 text-xs">
                  {t(`elements.${getZodiacInfo(chartData.sunSign)?.element}`)}
                </Badge>
              </div>

              {/* 月亮星座 */}
              <div className="text-center p-6 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                <div className="text-4xl mb-2">{getZodiacInfo(chartData.moonSign)?.symbol}</div>
                <div className="text-lg font-semibold text-blue-700 dark:text-blue-300 mb-1">
                  {t('signs.moon')}
                </div>
                <div className="text-sm font-medium text-blue-600 dark:text-blue-400">
                  {getZodiacInfo(chartData.moonSign)?.name}
                </div>
                <Badge variant="outline" className="mt-2 text-xs">
                  {t(`elements.${getZodiacInfo(chartData.moonSign)?.element}`)}
                </Badge>
              </div>

              {/* 上升星座 */}
              <div className="text-center p-6 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg border border-purple-200 dark:border-purple-700">
                <div className="text-4xl mb-2">{getZodiacInfo(chartData.ascendant)?.symbol}</div>
                <div className="text-lg font-semibold text-purple-700 dark:text-purple-300 mb-1">
                  {t('signs.ascendant')}
                </div>
                <div className="text-sm font-medium text-purple-600 dark:text-purple-400">
                  {getZodiacInfo(chartData.ascendant)?.name}
                </div>
                <Badge variant="outline" className="mt-2 text-xs">
                  {t(`elements.${getZodiacInfo(chartData.ascendant)?.element}`)}
                </Badge>
              </div>
            </div>

            {/* AI解读按钮 */}
            <div className="text-center">
              <Button
                onClick={() => setShowAIReading(true)}
                className="bg-gradient-to-r from-mystical-500 to-mystical-600 hover:from-mystical-600 hover:to-mystical-700"
              >
                {t('getAIReading')}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI解读 */}
      {chartData && showAIReading && (
        <AstrologyAIReader
          birthDate={chartData.birthDate}
          birthTime={chartData.birthTime}
          birthPlace={chartData.birthPlace}
          sunSign={getZodiacInfo(chartData.sunSign)?.name || ''}
          moonSign={getZodiacInfo(chartData.moonSign)?.name || ''}
          ascendant={getZodiacInfo(chartData.ascendant)?.name || ''}
          locale={locale}
        />
      )}
    </div>
  );
}
