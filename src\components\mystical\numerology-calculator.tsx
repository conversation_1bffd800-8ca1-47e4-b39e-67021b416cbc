'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { NumerologyAIReader } from '@/components/ai/ai-mystical-reader';
import { Calculator, Hash } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { Locale } from '@/types';

interface NumerologyResult {
  lifePathNumber: number;
  expressionNumber: number;
  soulUrgeNumber: number;
  personalityNumber: number;
  birthdayNumber: number;
}

interface NumerologyCalculatorProps {
  locale: Locale;
  className?: string;
}

export function NumerologyCalculator({ locale, className = '' }: NumerologyCalculatorProps) {
  const t = useTranslations('numerology');
  const [fullName, setFullName] = useState('');
  const [birthDate, setBirthDate] = useState('');
  const [result, setResult] = useState<NumerologyResult | null>(null);
  const [showAIReading, setShowAIReading] = useState(false);

  // 计算生命路径数
  const calculateLifePathNumber = (dateString: string): number => {
    const date = new Date(dateString);
    const day = date.getDate();
    const month = date.getMonth() + 1;
    const year = date.getFullYear();
    
    const sum = day + month + year;
    return reduceToSingleDigit(sum);
  };

  // 计算表达数（基于全名）
  const calculateExpressionNumber = (name: string): number => {
    const letterValues: Record<string, number> = {
      a: 1, b: 2, c: 3, d: 4, e: 5, f: 6, g: 7, h: 8, i: 9,
      j: 1, k: 2, l: 3, m: 4, n: 5, o: 6, p: 7, q: 8, r: 9,
      s: 1, t: 2, u: 3, v: 4, w: 5, x: 6, y: 7, z: 8
    };

    const cleanName = name.toLowerCase().replace(/[^a-z]/g, '');
    const sum = cleanName.split('').reduce((acc, letter) => {
      return acc + (letterValues[letter] || 0);
    }, 0);

    return reduceToSingleDigit(sum);
  };

  // 计算心灵数（基于元音）
  const calculateSoulUrgeNumber = (name: string): number => {
    const vowels = 'aeiou';
    const letterValues: Record<string, number> = {
      a: 1, e: 5, i: 9, o: 6, u: 3
    };

    const cleanName = name.toLowerCase().replace(/[^a-z]/g, '');
    const sum = cleanName.split('').reduce((acc, letter) => {
      if (vowels.includes(letter)) {
        return acc + (letterValues[letter] || 0);
      }
      return acc;
    }, 0);

    return reduceToSingleDigit(sum);
  };

  // 计算个性数（基于辅音）
  const calculatePersonalityNumber = (name: string): number => {
    const vowels = 'aeiou';
    const letterValues: Record<string, number> = {
      b: 2, c: 3, d: 4, f: 6, g: 7, h: 8, j: 1, k: 2, l: 3, m: 4,
      n: 5, p: 7, q: 8, r: 9, s: 1, t: 2, v: 4, w: 5, x: 6, y: 7, z: 8
    };

    const cleanName = name.toLowerCase().replace(/[^a-z]/g, '');
    const sum = cleanName.split('').reduce((acc, letter) => {
      if (!vowels.includes(letter)) {
        return acc + (letterValues[letter] || 0);
      }
      return acc;
    }, 0);

    return reduceToSingleDigit(sum);
  };

  // 计算生日数
  const calculateBirthdayNumber = (dateString: string): number => {
    const date = new Date(dateString);
    const day = date.getDate();
    return reduceToSingleDigit(day);
  };

  // 将数字减少到单位数（除了11、22、33这些主数）
  const reduceToSingleDigit = (num: number): number => {
    while (num > 9 && num !== 11 && num !== 22 && num !== 33) {
      num = num.toString().split('').reduce((acc, digit) => acc + parseInt(digit), 0);
    }
    return num;
  };

  // 执行计算
  const calculate = () => {
    if (!fullName.trim() || !birthDate) return;

    const result: NumerologyResult = {
      lifePathNumber: calculateLifePathNumber(birthDate),
      expressionNumber: calculateExpressionNumber(fullName),
      soulUrgeNumber: calculateSoulUrgeNumber(fullName),
      personalityNumber: calculatePersonalityNumber(fullName),
      birthdayNumber: calculateBirthdayNumber(birthDate),
    };

    setResult(result);
    setShowAIReading(false);
  };

  // 重置
  const reset = () => {
    setFullName('');
    setBirthDate('');
    setResult(null);
    setShowAIReading(false);
  };

  // 获取数字含义
  const getNumberMeaning = (number: number, type: string) => {
    const meanings: Record<string, Record<number, string>> = {
      lifePath: {
        1: t('meanings.lifePath.1'),
        2: t('meanings.lifePath.2'),
        3: t('meanings.lifePath.3'),
        4: t('meanings.lifePath.4'),
        5: t('meanings.lifePath.5'),
        6: t('meanings.lifePath.6'),
        7: t('meanings.lifePath.7'),
        8: t('meanings.lifePath.8'),
        9: t('meanings.lifePath.9'),
        11: t('meanings.lifePath.11'),
        22: t('meanings.lifePath.22'),
        33: t('meanings.lifePath.33'),
      },
      // 可以添加其他类型的含义
    };

    return meanings[type]?.[number] || t('meanings.unknown');
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 输入表单 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calculator className="h-5 w-5 text-mystical-500" />
            {t('calculator.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 姓名输入 */}
          <div className="space-y-2">
            <Label htmlFor="fullName">{t('input.fullName')}</Label>
            <Input
              id="fullName"
              type="text"
              placeholder={t('input.fullNamePlaceholder')}
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
            />
          </div>

          {/* 出生日期输入 */}
          <div className="space-y-2">
            <Label htmlFor="birthDate">{t('input.birthDate')}</Label>
            <Input
              id="birthDate"
              type="date"
              value={birthDate}
              onChange={(e) => setBirthDate(e.target.value)}
            />
          </div>

          {/* 计算按钮 */}
          <div className="flex gap-2">
            <Button
              onClick={calculate}
              disabled={!fullName.trim() || !birthDate}
              className="flex-1 bg-gradient-to-r from-mystical-500 to-mystical-600 hover:from-mystical-600 hover:to-mystical-700"
            >
              <Calculator className="mr-2 h-4 w-4" />
              {t('calculate')}
            </Button>
            
            {result && (
              <Button onClick={reset} variant="outline">
                {t('reset')}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 计算结果 */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Hash className="h-5 w-5 text-mystical-500" />
              {t('results.title')}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* 核心数字 */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* 生命路径数 */}
              <div className="text-center p-4 bg-gradient-to-br from-mystical-50 to-purple-50 dark:from-mystical-900/20 dark:to-purple-900/20 rounded-lg border border-mystical-200 dark:border-mystical-700">
                <div className="text-3xl font-bold text-mystical-600 dark:text-mystical-400 mb-2">
                  {result.lifePathNumber}
                </div>
                <div className="text-sm font-medium text-mystical-700 dark:text-mystical-300 mb-1">
                  {t('numbers.lifePath')}
                </div>
                <div className="text-xs text-mystical-500 dark:text-mystical-400">
                  {getNumberMeaning(result.lifePathNumber, 'lifePath')}
                </div>
              </div>

              {/* 表达数 */}
              <div className="text-center p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                <div className="text-3xl font-bold text-blue-600 dark:text-blue-400 mb-2">
                  {result.expressionNumber}
                </div>
                <div className="text-sm font-medium text-blue-700 dark:text-blue-300 mb-1">
                  {t('numbers.expression')}
                </div>
                <div className="text-xs text-blue-500 dark:text-blue-400">
                  {t('descriptions.expression')}
                </div>
              </div>

              {/* 心灵数 */}
              <div className="text-center p-4 bg-gradient-to-br from-pink-50 to-rose-50 dark:from-pink-900/20 dark:to-rose-900/20 rounded-lg border border-pink-200 dark:border-pink-700">
                <div className="text-3xl font-bold text-pink-600 dark:text-pink-400 mb-2">
                  {result.soulUrgeNumber}
                </div>
                <div className="text-sm font-medium text-pink-700 dark:text-pink-300 mb-1">
                  {t('numbers.soulUrge')}
                </div>
                <div className="text-xs text-pink-500 dark:text-pink-400">
                  {t('descriptions.soulUrge')}
                </div>
              </div>

              {/* 个性数 */}
              <div className="text-center p-4 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border border-green-200 dark:border-green-700">
                <div className="text-3xl font-bold text-green-600 dark:text-green-400 mb-2">
                  {result.personalityNumber}
                </div>
                <div className="text-sm font-medium text-green-700 dark:text-green-300 mb-1">
                  {t('numbers.personality')}
                </div>
                <div className="text-xs text-green-500 dark:text-green-400">
                  {t('descriptions.personality')}
                </div>
              </div>

              {/* 生日数 */}
              <div className="text-center p-4 bg-gradient-to-br from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 rounded-lg border border-orange-200 dark:border-orange-700">
                <div className="text-3xl font-bold text-orange-600 dark:text-orange-400 mb-2">
                  {result.birthdayNumber}
                </div>
                <div className="text-sm font-medium text-orange-700 dark:text-orange-300 mb-1">
                  {t('numbers.birthday')}
                </div>
                <div className="text-xs text-orange-500 dark:text-orange-400">
                  {t('descriptions.birthday')}
                </div>
              </div>
            </div>

            {/* AI解读按钮 */}
            <div className="text-center">
              <Button
                onClick={() => setShowAIReading(true)}
                className="bg-gradient-to-r from-mystical-500 to-mystical-600 hover:from-mystical-600 hover:to-mystical-700"
              >
                {t('getAIReading')}
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI解读 */}
      {result && showAIReading && (
        <NumerologyAIReader
          lifePathNumber={result.lifePathNumber}
          birthDate={birthDate}
          fullName={fullName}
          locale={locale}
        />
      )}
    </div>
  );
}
