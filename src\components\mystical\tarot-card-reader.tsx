'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TarotAIReader } from '@/components/ai/ai-mystical-reader';
import { Shuffle, RotateCcw } from 'lucide-react';
import { useTranslations } from 'next-intl';
import type { Locale } from '@/types';

// 塔罗牌数据
const TAROT_CARDS = [
  // 大阿卡纳
  { id: 'fool', name: 'The Fool', nameZh: '愚者', type: 'major' },
  { id: 'magician', name: 'The Magician', nameZh: '魔术师', type: 'major' },
  { id: 'high-priestess', name: 'The High Priestess', nameZh: '女祭司', type: 'major' },
  { id: 'empress', name: 'The Empress', nameZh: '皇后', type: 'major' },
  { id: 'emperor', name: 'The Emperor', nameZh: '皇帝', type: 'major' },
  { id: 'hierophant', name: 'The Hierophant', nameZh: '教皇', type: 'major' },
  { id: 'lovers', name: 'The Lovers', nameZh: '恋人', type: 'major' },
  { id: 'chariot', name: 'The Chariot', nameZh: '战车', type: 'major' },
  { id: 'strength', name: 'Strength', nameZh: '力量', type: 'major' },
  { id: 'hermit', name: 'The Hermit', nameZh: '隐者', type: 'major' },
  { id: 'wheel-fortune', name: 'Wheel of Fortune', nameZh: '命运之轮', type: 'major' },
  { id: 'justice', name: 'Justice', nameZh: '正义', type: 'major' },
  { id: 'hanged-man', name: 'The Hanged Man', nameZh: '倒吊人', type: 'major' },
  { id: 'death', name: 'Death', nameZh: '死神', type: 'major' },
  { id: 'temperance', name: 'Temperance', nameZh: '节制', type: 'major' },
  { id: 'devil', name: 'The Devil', nameZh: '恶魔', type: 'major' },
  { id: 'tower', name: 'The Tower', nameZh: '塔', type: 'major' },
  { id: 'star', name: 'The Star', nameZh: '星星', type: 'major' },
  { id: 'moon', name: 'The Moon', nameZh: '月亮', type: 'major' },
  { id: 'sun', name: 'The Sun', nameZh: '太阳', type: 'major' },
  { id: 'judgement', name: 'Judgement', nameZh: '审判', type: 'major' },
  { id: 'world', name: 'The World', nameZh: '世界', type: 'major' },
  
  // 小阿卡纳 - 权杖
  { id: 'ace-wands', name: 'Ace of Wands', nameZh: '权杖王牌', type: 'minor' },
  { id: 'two-wands', name: 'Two of Wands', nameZh: '权杖二', type: 'minor' },
  { id: 'three-wands', name: 'Three of Wands', nameZh: '权杖三', type: 'minor' },
  // ... 可以继续添加更多牌
];

interface TarotReading {
  card: typeof TAROT_CARDS[0];
  orientation: 'upright' | 'reversed';
  question: string;
  background: string;
}

interface TarotCardReaderProps {
  locale: Locale;
  className?: string;
}

export function TarotCardReader({ locale, className = '' }: TarotCardReaderProps) {
  const t = useTranslations('tarot');
  const [reading, setReading] = useState<TarotReading | null>(null);
  const [question, setQuestion] = useState('');
  const [background, setBackground] = useState('');
  const [selectedCard, setSelectedCard] = useState<string>('');
  const [orientation, setOrientation] = useState<'upright' | 'reversed'>('upright');
  const [isDrawing, setIsDrawing] = useState(false);

  // 随机抽牌
  const drawRandomCard = () => {
    setIsDrawing(true);
    
    // 模拟抽牌动画
    setTimeout(() => {
      const randomCard = TAROT_CARDS[Math.floor(Math.random() * TAROT_CARDS.length)];
      const randomOrientation = Math.random() > 0.5 ? 'upright' : 'reversed';
      
      setSelectedCard(randomCard.id);
      setOrientation(randomOrientation);
      setIsDrawing(false);
    }, 1500);
  };

  // 开始解读
  const startReading = () => {
    if (!selectedCard || !question.trim()) return;

    const card = TAROT_CARDS.find(c => c.id === selectedCard);
    if (!card) return;

    setReading({
      card,
      orientation,
      question: question.trim(),
      background: background.trim(),
    });
  };

  // 重置
  const reset = () => {
    setReading(null);
    setQuestion('');
    setBackground('');
    setSelectedCard('');
    setOrientation('upright');
  };

  const getCardName = (card: typeof TAROT_CARDS[0]) => {
    return locale === 'zh' ? card.nameZh : card.name;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 塔罗牌选择和设置 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shuffle className="h-5 w-5 text-mystical-500" />
            {t('cardSelection.title')}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* 问题输入 */}
          <div className="space-y-2">
            <Label htmlFor="question">{t('question.label')}</Label>
            <Textarea
              id="question"
              placeholder={t('question.placeholder')}
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              className="min-h-[80px]"
            />
          </div>

          {/* 背景信息 */}
          <div className="space-y-2">
            <Label htmlFor="background">{t('background.label')}</Label>
            <Textarea
              id="background"
              placeholder={t('background.placeholder')}
              value={background}
              onChange={(e) => setBackground(e.target.value)}
              className="min-h-[60px]"
            />
          </div>

          {/* 抽牌方式选择 */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* 随机抽牌 */}
            <div className="space-y-2">
              <Label>{t('drawMethod.random')}</Label>
              <Button
                onClick={drawRandomCard}
                disabled={isDrawing || !question.trim()}
                className="w-full"
                variant="outline"
              >
                {isDrawing ? (
                  <>
                    <Shuffle className="mr-2 h-4 w-4 animate-spin" />
                    {t('drawing')}
                  </>
                ) : (
                  <>
                    <Shuffle className="mr-2 h-4 w-4" />
                    {t('drawCard')}
                  </>
                )}
              </Button>
            </div>

            {/* 手动选择 */}
            <div className="space-y-2">
              <Label>{t('drawMethod.manual')}</Label>
              <Select value={selectedCard} onValueChange={setSelectedCard}>
                <SelectTrigger>
                  <SelectValue placeholder={t('selectCard')} />
                </SelectTrigger>
                <SelectContent>
                  {TAROT_CARDS.map((card) => (
                    <SelectItem key={card.id} value={card.id}>
                      {getCardName(card)}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* 牌面方向 */}
          {selectedCard && (
            <div className="space-y-2">
              <Label>{t('orientation.label')}</Label>
              <Select value={orientation} onValueChange={(value: 'upright' | 'reversed') => setOrientation(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="upright">{t('orientation.upright')}</SelectItem>
                  <SelectItem value="reversed">{t('orientation.reversed')}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          )}

          {/* 开始解读按钮 */}
          <div className="flex gap-2">
            <Button
              onClick={startReading}
              disabled={!selectedCard || !question.trim()}
              className="flex-1 bg-gradient-to-r from-mystical-500 to-mystical-600 hover:from-mystical-600 hover:to-mystical-700"
            >
              {t('startReading')}
            </Button>
            
            {reading && (
              <Button onClick={reset} variant="outline">
                <RotateCcw className="mr-2 h-4 w-4" />
                {t('reset')}
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 选中的牌显示 */}
      {selectedCard && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center space-y-2">
              <div className="w-32 h-48 mx-auto bg-gradient-to-br from-mystical-100 to-purple-100 dark:from-mystical-800 dark:to-purple-800 border-2 border-mystical-300 dark:border-mystical-600 rounded-lg flex items-center justify-center">
                <div className={`text-center ${orientation === 'reversed' ? 'rotate-180' : ''}`}>
                  <div className="text-lg font-semibold text-mystical-700 dark:text-mystical-300">
                    {getCardName(TAROT_CARDS.find(c => c.id === selectedCard)!)}
                  </div>
                  <div className="text-sm text-mystical-500 dark:text-mystical-400 mt-2">
                    {t(`orientation.${orientation}`)}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* AI解读 */}
      {reading && (
        <TarotAIReader
          cardName={getCardName(reading.card)}
          orientation={reading.orientation}
          question={reading.question}
          background={reading.background}
          locale={locale}
        />
      )}
    </div>
  );
}
