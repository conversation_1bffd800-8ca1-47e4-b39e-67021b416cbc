/**
 * 分析和监控工具库
 * 集成多种分析服务，包括Umami、Google Analytics等
 */

// 分析事件类型
export interface AnalyticsEvent {
  name: string;
  properties?: Record<string, any>;
  userId?: string;
  sessionId?: string;
  timestamp?: number;
}

// 用户属性
export interface UserProperties {
  userId?: string;
  locale?: string;
  timezone?: string;
  userAgent?: string;
  referrer?: string;
  utm_source?: string;
  utm_medium?: string;
  utm_campaign?: string;
}

// 页面浏览事件
export interface PageViewEvent {
  path: string;
  title: string;
  referrer?: string;
  locale?: string;
  userId?: string;
}

// 自定义事件
export interface CustomEvent {
  category: string;
  action: string;
  label?: string;
  value?: number;
  properties?: Record<string, any>;
}

class AnalyticsManager {
  private isInitialized = false;
  private userId: string | null = null;
  private sessionId: string;
  private userProperties: UserProperties = {};

  constructor() {
    this.sessionId = this.generateSessionId();
    
    if (typeof window !== 'undefined') {
      this.initializeAnalytics();
    }
  }

  // 初始化分析服务
  private initializeAnalytics() {
    if (this.isInitialized) return;

    // 初始化Umami
    this.initializeUmami();
    
    // 初始化Google Analytics（如果配置了）
    this.initializeGoogleAnalytics();
    
    // 设置页面卸载时的数据发送
    this.setupBeforeUnload();
    
    this.isInitialized = true;
  }

  // 初始化Umami
  private initializeUmami() {
    if (!process.env.NEXT_PUBLIC_UMAMI_WEBSITE_ID) return;

    // 动态加载Umami脚本
    const script = document.createElement('script');
    script.async = true;
    script.defer = true;
    script.src = process.env.NEXT_PUBLIC_UMAMI_URL || 'https://analytics.umami.is/script.js';
    script.setAttribute('data-website-id', process.env.NEXT_PUBLIC_UMAMI_WEBSITE_ID);
    script.setAttribute('data-domains', process.env.NEXT_PUBLIC_SITE_DOMAIN || 'mystical-insights.com');
    
    // 配置Umami选项
    script.setAttribute('data-auto-track', 'true');
    script.setAttribute('data-do-not-track', 'true');
    script.setAttribute('data-cache', 'true');
    
    document.head.appendChild(script);
  }

  // 初始化Google Analytics
  private initializeGoogleAnalytics() {
    const gaId = process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID;
    if (!gaId) return;

    // 加载gtag脚本
    const script1 = document.createElement('script');
    script1.async = true;
    script1.src = `https://www.googletagmanager.com/gtag/js?id=${gaId}`;
    document.head.appendChild(script1);

    // 初始化gtag
    const script2 = document.createElement('script');
    script2.innerHTML = `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${gaId}', {
        page_title: document.title,
        page_location: window.location.href,
        anonymize_ip: true,
        allow_google_signals: false,
        allow_ad_personalization_signals: false
      });
    `;
    document.head.appendChild(script2);

    // 设置全局gtag函数
    (window as any).gtag = (window as any).gtag || function() {
      ((window as any).dataLayer = (window as any).dataLayer || []).push(arguments);
    };
  }

  // 设置页面卸载时的数据发送
  private setupBeforeUnload() {
    window.addEventListener('beforeunload', () => {
      this.flush();
    });

    // 页面可见性变化时也发送数据
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'hidden') {
        this.flush();
      }
    });
  }

  // 生成会话ID
  private generateSessionId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // 设置用户ID
  setUserId(userId: string) {
    this.userId = userId;
    
    // 更新Google Analytics用户ID
    if ((window as any).gtag) {
      (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
        user_id: userId
      });
    }
  }

  // 设置用户属性
  setUserProperties(properties: UserProperties) {
    this.userProperties = { ...this.userProperties, ...properties };
    
    // 更新Google Analytics用户属性
    if ((window as any).gtag) {
      (window as any).gtag('set', properties);
    }
  }

  // 跟踪页面浏览
  trackPageView(event: PageViewEvent) {
    // Umami自动跟踪页面浏览，这里主要处理Google Analytics
    if ((window as any).gtag) {
      (window as any).gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
        page_title: event.title,
        page_location: window.location.href,
        page_path: event.path,
        custom_map: {
          custom_dimension_1: event.locale,
          custom_dimension_2: event.userId,
        }
      });
    }

    // 发送到自定义分析端点
    this.sendCustomEvent({
      name: 'page_view',
      properties: {
        path: event.path,
        title: event.title,
        referrer: event.referrer,
        locale: event.locale,
        timestamp: Date.now(),
      },
      userId: event.userId,
    });
  }

  // 跟踪自定义事件
  trackEvent(event: CustomEvent) {
    // Google Analytics事件
    if ((window as any).gtag) {
      (window as any).gtag('event', event.action, {
        event_category: event.category,
        event_label: event.label,
        value: event.value,
        custom_parameters: event.properties,
      });
    }

    // Umami事件（如果支持）
    if ((window as any).umami) {
      (window as any).umami.track(event.action, event.properties);
    }

    // 发送到自定义分析端点
    this.sendCustomEvent({
      name: `${event.category}_${event.action}`,
      properties: {
        category: event.category,
        action: event.action,
        label: event.label,
        value: event.value,
        ...event.properties,
      },
    });
  }

  // 跟踪AI使用情况
  trackAIUsage(data: {
    type: string;
    promptId: string;
    provider: string;
    tokensUsed: number;
    responseTime: number;
    success: boolean;
    locale: string;
  }) {
    this.trackEvent({
      category: 'ai_usage',
      action: data.type,
      label: data.promptId,
      value: data.tokensUsed,
      properties: {
        provider: data.provider,
        response_time: data.responseTime,
        success: data.success,
        locale: data.locale,
        timestamp: Date.now(),
      },
    });
  }

  // 跟踪用户交互
  trackUserInteraction(element: string, action: string, properties?: Record<string, any>) {
    this.trackEvent({
      category: 'user_interaction',
      action: action,
      label: element,
      properties: {
        element,
        ...properties,
        timestamp: Date.now(),
      },
    });
  }

  // 跟踪错误
  trackError(error: Error, context?: Record<string, any>) {
    this.trackEvent({
      category: 'error',
      action: 'javascript_error',
      label: error.message,
      properties: {
        error_name: error.name,
        error_message: error.message,
        error_stack: error.stack,
        ...context,
        timestamp: Date.now(),
      },
    });
  }

  // 跟踪性能指标
  trackPerformance(metric: string, value: number, properties?: Record<string, any>) {
    this.trackEvent({
      category: 'performance',
      action: metric,
      value: Math.round(value),
      properties: {
        metric,
        value,
        ...properties,
        timestamp: Date.now(),
      },
    });
  }

  // 发送自定义事件到后端
  private async sendCustomEvent(event: AnalyticsEvent) {
    if (process.env.NODE_ENV !== 'production') {
      console.log('Analytics Event:', event);
      return;
    }

    try {
      const eventData = {
        ...event,
        userId: event.userId || this.userId,
        sessionId: this.sessionId,
        timestamp: event.timestamp || Date.now(),
        userProperties: this.userProperties,
        url: window.location.href,
        userAgent: navigator.userAgent,
      };

      // 使用sendBeacon确保数据发送
      if (navigator.sendBeacon) {
        navigator.sendBeacon('/api/analytics/events', JSON.stringify(eventData));
      } else {
        // 降级到fetch
        fetch('/api/analytics/events', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(eventData),
          keepalive: true,
        }).catch(console.error);
      }
    } catch (error) {
      console.error('Failed to send analytics event:', error);
    }
  }

  // 刷新待发送的数据
  private flush() {
    // 这里可以实现批量发送逻辑
    console.log('Flushing analytics data...');
  }

  // 获取会话信息
  getSessionInfo() {
    return {
      sessionId: this.sessionId,
      userId: this.userId,
      userProperties: this.userProperties,
    };
  }
}

// 单例实例
export const analytics = new AnalyticsManager();

// 便捷函数
export const trackPageView = (event: PageViewEvent) => analytics.trackPageView(event);
export const trackEvent = (event: CustomEvent) => analytics.trackEvent(event);
export const trackAIUsage = (data: Parameters<typeof analytics.trackAIUsage>[0]) => analytics.trackAIUsage(data);
export const trackUserInteraction = (element: string, action: string, properties?: Record<string, any>) => 
  analytics.trackUserInteraction(element, action, properties);
export const trackError = (error: Error, context?: Record<string, any>) => analytics.trackError(error, context);
export const trackPerformance = (metric: string, value: number, properties?: Record<string, any>) => 
  analytics.trackPerformance(metric, value, properties);
export const setUserId = (userId: string) => analytics.setUserId(userId);
export const setUserProperties = (properties: UserProperties) => analytics.setUserProperties(properties);
